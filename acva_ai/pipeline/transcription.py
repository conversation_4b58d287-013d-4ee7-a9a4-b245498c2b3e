import asyncio
import logging
import traceback
from typing import Op<PERSON>, Tuple

from acva_ai.audio_processing.audio_splitting import (
    merge_audio_chunks_by_duration,
    split_audio_segment_by_silence,
)
from acva_ai.database import mongo_instance
from acva_ai.llm.transcript_orchestrator import (
    TranscriptOrchestrator,
    TranscriptProvider,
)
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

# Set up logging
logger = logging.getLogger(__name__)

from typing import Dict

from pydub import AudioSegment


async def _generate_transcription(
    audio_segment: AudioSegment,
    transcript_provider: TranscriptProvider,
    batch_size: int = 50,
    min_duration_seconds: float = 5 * 60,
    max_duration_seconds: float = 10 * 60,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
) -> Tuple[str, Dict[str, str]]:

    if transcript_provider is None:
        raise ValueError("Transcript provider must be specified")

    audio_segment_chunks = split_audio_segment_by_silence(
        audio_segment=audio_segment, max_duration_seconds=max_duration_seconds
    )
    print(f"Initial split created {len(audio_segment_chunks)} chunks")

    # Merge chunks to fit within min/max duration
    merged_chunks = merge_audio_chunks_by_duration(
        audio_segment_chunks,
        min_duration_seconds=min_duration_seconds,
        max_duration_seconds=max_duration_seconds,
    )
    print(f"After merging, {len(merged_chunks)} chunks remain")

    audio_segment_chunks_batches = [
        merged_chunks[i : i + batch_size]
        for i in range(0, len(merged_chunks), batch_size)
    ]

    # Create transcript orchestrator
    orchestrator = TranscriptOrchestrator(
        primary_provider=transcript_provider,
        fallback_providers=None,  # Use default fallbacks
    )

    result_transcriptions = []

    chunks_status = {}
    current_timestamp = 0.0

    for batch in audio_segment_chunks_batches:
        tasks = [
            orchestrator.transcribe_audio_segment(
                audio_segment=audio_segment_chunk,
                language=langauge,
                response_usage=response_usage,
            )
            for audio_segment_chunk in batch
        ]
        results = await asyncio.gather(*tasks)

        for result, audio_segment_chunk in zip(results, batch):
            if len(result):
                chunks_status[current_timestamp] = "Transcript generated successfully"
                result_transcriptions.append(result)
            else:
                chunks_status[current_timestamp] = "Error in generating transcript"
            # TODO Add here check for repetitions and other checks

            current_timestamp += audio_segment_chunk.duration_seconds

    result_transcript = " ".join(result_transcriptions)
    timestamps = list(chunks_status.keys()) + [current_timestamp]

    chunks_status_dict = {
        f"{int(start)}-{int(end)}": "{status}"
        for start, end, status in zip(
            timestamps[:-1], timestamps[1:], chunks_status.values()
        )
    }
    return result_transcript, chunks_status_dict


async def generate_transcription(
    task_id: str,
    audio_segment: AudioSegment,
    processing_status: Optional[ProcessingStatus],
    visit_report: Optional[VisitReport],
    transcript_provider: TranscriptProvider,
    batch_size: int = 50,
    min_duration_seconds: float = 5 * 60,
    max_duration_seconds: float = 10 * 60,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
):
    if transcript_provider is None:
        raise ValueError("Transcript provider must be specified")

    """
    Processes an AudioSegment and generates the trasncription
    """
    logger.info(f"[Task {task_id}] Starting transcription generation")
    processing_status.start_stage("transcription")
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    try:
        result_transcript, chunks_status_dict = await _generate_transcription(
            audio_segment=audio_segment,
            batch_size=batch_size,
            min_duration_seconds=min_duration_seconds,
            max_duration_seconds=max_duration_seconds,
            langauge=langauge,
            response_usage=response_usage,
            transcript_provider=transcript_provider,
        )
        visit_report.raw_transcript = result_transcript
        visit_report.transcript_observations = [
            f"{k}: {v}" for k, v in chunks_status_dict.items()
        ]
        processing_status.complete_stage("transcription")
        logger.info(f"[Task {task_id}] Completed transcription successfully")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("transcription", e, stack_trace)
        processing_status.finalize()
        logger.error(f"[Task {task_id}] Error generating transcript {e}\n{stack_trace}")

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())


def test():
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    audio_segment = AudioSegment.from_file(sample_audio_path)

    # Process the audio segment
    result_transcript, status_dict = asyncio.run(
        _generate_transcription(audio_segment=audio_segment)
    )
    print(result_transcript)
    print(status_dict)


if __name__ == "__main__":
    test()
