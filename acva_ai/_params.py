import os

from dotenv import load_dotenv

load_dotenv()

API_KEY = os.getenv("API_KEY")
#
# API_KEY_VATIS = os.getenv("API_KEY_VETIS")
# if not API_KEY_VATIS:
#     raise ValueError("API_KEY_VETIS is required")

ACVA_CALLBACK_URL = os.getenv("ACVA_CALLBACK_URL")
ACVA_REPORT_FIELDS = os.getenv("ACVA_REPORT_FIELDS")

MONGODB = os.getenv("MONGODB")
OPENAI_API = os.getenv("OPENAI_KEY")
FIRECRAWL_API = os.getenv("FIRECRAWL_API")
BRAVE_API = os.getenv("BRAVE_API")
CACHE_DIR = os.getenv("CACHE_DIR")
QDRANT_SERVER = os.getenv("QDRANT_SERVER")

MINIO_SERVER = os.getenv("MINIO_SERVER")
MINIO_PORT = os.getenv("MINIO_PORT")
MINIO_ROOT_USER = os.getenv("MINIO_ROOT_USER")
MINIO_ROOT_PASSWORD = os.getenv("MINIO_ROOT_PASSWORD")

TMP_DATA_DIR = os.getenv("TMP_DATA_DIR")
os.makedirs(TMP_DATA_DIR, exist_ok=True)

TRANSCRIPT_DIR = os.path.join(TMP_DATA_DIR, "transcripts")
os.makedirs(TRANSCRIPT_DIR, exist_ok=True)

AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
OPENAI_TRANSCRIPTION_API_KEY = os.getenv("OPENAI_TRANSCRIPTION_API_KEY")

# ElevenLabs configuration
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
ELEVENLABS_MODEL_ID = "scribe_v1"
ELEVENLABS_MAX_AUDIO_SIZE = 1000.0  # 1GB limit for ElevenLabs

# Transcript provider configuration
DEFAULT_TRANSCRIPT_PROVIDER = os.getenv(
    "DEFAULT_TRANSCRIPT_PROVIDER", "elevenlabs"
).lower()

FALLBACK_TRANSCRIPT_PROVIDER = ["openai", "azure"]


CACHE_DAYS_RELOAD = 30
AZURE_OPENAI_ENDPOINT = "https://acva-openai-llm.openai.azure.com"
AZURE_API_VERSION = "2024-12-01-preview"
OPENAI_MODEL_ID = "gpt-4o"

AZURE_OPENAI_TRANSCRIPTION_ENDPOINT = "https://acva-openai-llm-us.openai.azure.com"  # The deployment of Transcribe model is not available in europe
AZURE_TRANSCRIPTION_API_VERSION = "2025-03-01-preview"
OPENAI_TRANSCRIPTION_MODEL_ID = "gpt-4o-transcribe"

LAZARUS_AUTHKEY = os.getenv("LAZARUS_AUTHKEY")
LAZARUS_ORGID = os.getenv("LAZARUS_ORGID")

COSMOS_DB_CONNECTION_STRING = os.getenv("COSMOS_DB_CONNECTION_STRING")
MONGO_DB_CONNECTION_STRING = os.getenv("MONGO_DB_CONNECTION_STRING")

OPENAI_PRICING = {
    "gpt-4o": {"input": 2.50, "output": 10},
    "gpt-4o-mini": {"input": 0.15, "output": 0.60},
    "gpt-4o-transcribe": {"input": 2.5, "output": 10.0},
}
OPENAI_MAX_AUDIO_SIZE = 20.0  # MB
SILENCE_THRESHOLD_STARTING_DELTA = 30  # dBFS decrement for silence threshold
SILENCE_THRESHOLD_INCREMENT = 5  # dBFS increment for more aggressive silence detection
MIN_SILENCE_LEN_SECONDS = 2.0  # Minimum silence length to split on, in seconds
