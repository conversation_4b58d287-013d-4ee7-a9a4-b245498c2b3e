import logging
import os
import tempfile
import uuid
from typing import List, Optional

from fastapi import (
    APIRouter,
    Body,
    Depends,
    File,
    HTTPException,
    Query,
    UploadFile,
)
from fastapi.responses import JSONResponse

from acva_ai.llm.transcript_orchestrator import (
    TranscriptOrchestrator,
    TranscriptProvider,
)
from acva_ai.llm.scenarios.grammar import _grammar_check_by_chunks
from acva_ai.pipeline.affections_processing import verify_affections
from acva_ai.pipeline.domain_insights import _process_domain_insights
from acva_ai.pipeline.medical_report import build_medical_report
from acva_ai.pipeline.medication_processing import _process_medication
from acva_ai.utils.audio_utils import concatenate_audio_files, normalize_audio_segment
from acva_ai.utils.security_service import SecurityService
from acva_ai.utils.usage import ResponseUsage
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai._params import DEFAULT_TRANSCRIPT_PROVIDER

logger = logging.getLogger(__name__)

methods_router = APIRouter(
    tags=["Methods"], dependencies=[Depends(SecurityService.get_api_key)]
)


def validate_and_create_orchestrator(provider: Optional[str]) -> "LLMOrchestrator":
    """
    Validate provider parameter and create LLM orchestrator.

    Args:
        provider: Optional provider string (azure, openai, lazarus)

    Returns:
        LLMOrchestrator instance

    Raises:
        HTTPException: If provider is invalid
    """
    from acva_ai.llm.llm_orchestrator import LLMProvider, LLMOrchestrator

    llm_provider = None
    if provider:
        try:
            llm_provider = LLMProvider(provider.lower())
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
            )

    return LLMOrchestrator(primary_provider=llm_provider or LLMProvider.AZURE)


# Transcription routes
@methods_router.post("/transcription/transcribe")
async def transcribe_audio(
    files: List[UploadFile] = File(...),
    transcript_provider: TranscriptProvider = Query(
        TranscriptProvider(DEFAULT_TRANSCRIPT_PROVIDER),
        description="Transcript provider to use (azure, elevenlabs)",
    ),
):
    """
    Transcribes an audio file and returns the raw transcript.

    This endpoint handles the first step of the medical transcription pipeline:
    converting speech to text with confidence scores.

    - **file**: Audio file to transcribe (MP3, WAV, M4A formats supported)

    Returns a JSON object containing the `task_id` and `transcript`.
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create temporary directory if it doesn't exist
        temp_dir = os.path.join(tempfile.gettempdir(), "acva_ai")
        os.makedirs(temp_dir, exist_ok=True)

        # Concatenate multiple files into a single AudioSegment
        audio_segment = concatenate_audio_files(files)
        audio_segment = normalize_audio_segment(audio_segment)

        # Save the audio segment to a temporary file
        file_path = os.path.join(temp_dir, f"{task_id}.wav")
        audio_segment.export(file_path, format="wav")

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Create transcript orchestrator
        orchestrator = TranscriptOrchestrator(
            primary_provider=transcript_provider,
        )

        # Transcribe the audio
        transcript = await orchestrator.transcribe_audio_segment(
            audio_segment=audio_segment, response_usage=response_usage, use_cache=False
        )

        return JSONResponse({"task_id": task_id, "transcript": transcript})
    except Exception as e:
        logger.error(f"Error in transcribe_audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Grammar routes
@methods_router.post("/grammar/correct-grammar")
async def correct_grammar_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
):
    """
    Corrects grammar and improves the quality of a transcript.

    This endpoint handles the second step of the medical transcription pipeline:
    correcting grammar, fixing typos, and improving the overall quality of the transcript.

    - **transcript**: Transcript text to correct
    - **provider**: Optional LLM provider to use (azure, openai, lazarus)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Validate and convert provider
        llm_provider = None
        if provider:
            try:
                from acva_ai.llm.llm_orchestrator import LLMProvider

                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
                )

        # Create LLM orchestrator with specified provider
        from acva_ai.llm.llm_orchestrator import LLMOrchestrator

        orchestrator = validate_and_create_orchestrator(provider)

        # Correct grammar
        corrected_transcript, explanations = await _grammar_check_by_chunks(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        return JSONResponse(
            {"task_id": task_id, "corrected_transcript": corrected_transcript}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in correct_grammar_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Medication routes
@methods_router.post("/medication/extract-medications")
async def extract_medications(
    transcript: str = Body(..., media_type="text/plain"),
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
):
    """
    Extracts medication information from a transcript.

    This endpoint handles the third step of the medical transcription pipeline:
    identifying medications mentioned in the transcript along with their context,
    and enriching them with additional information from medical databases.

    - **transcript**: Transcript text to analyze for medications
    - **provider**: Optional LLM provider to use (azure, openai, lazarus)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Validate and convert provider
        llm_provider = None
        if provider:
            try:
                from acva_ai.llm.llm_orchestrator import LLMProvider

                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
                )

        # Create LLM orchestrator with specified provider
        from acva_ai.llm.llm_orchestrator import LLMOrchestrator

        orchestrator = validate_and_create_orchestrator(provider)

        # Extract medications
        medications = await _process_medication(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        # Convert list of Medication objects to list of dictionaries
        medications_list = [med.model_dump() for med in medications]

        return JSONResponse({"task_id": task_id, "medications": medications_list})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_medications: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Affection routes
@methods_router.post("/affection/extract-affections")
async def extract_affections(
    transcript: str = Body(..., media_type="text/plain"),
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
):
    """
    Extracts medical conditions and affections from a transcript.

    This endpoint handles the fourth step of the medical transcription pipeline:
    identifying medical conditions, symptoms, and diagnoses mentioned in the transcript,
    and verifying them against medical terminology databases.

    - **transcript**: Transcript text to analyze for medical conditions
    - **provider**: Optional LLM provider to use (azure, openai, lazarus)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Validate and convert provider
        llm_provider = None
        if provider:
            try:
                from acva_ai.llm.llm_orchestrator import LLMProvider

                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
                )

        # Create LLM orchestrator with specified provider
        from acva_ai.llm.llm_orchestrator import LLMOrchestrator

        orchestrator = validate_and_create_orchestrator(provider)

        # Extract affections
        affections = await verify_affections(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        # Convert list of Affection objects to list of dictionaries
        affections_list = [affection.model_dump() for affection in affections]

        return JSONResponse({"task_id": task_id, "affections": affections_list})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_affections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Medical note routes
@methods_router.post("/medical-report/generate-medical-note")
async def generate_medical_note_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
):
    """
    Generates a structured medical note from a transcript.

    This endpoint handles the fifth step of the medical transcription pipeline:
    generating a structured medical note with sections like chief complaint,
    history of present illness, medications, etc.

    - **transcript**: Transcript text to generate a medical note from
    - **provider**: Optional LLM provider to use (azure, openai, lazarus)
    """
    try:
        # Create a unique task ID
        task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Validate and convert provider
        llm_provider = None
        if provider:
            try:
                from acva_ai.llm.llm_orchestrator import LLMProvider

                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
                )

        # Create LLM orchestrator with specified provider
        orchestrator = validate_and_create_orchestrator(provider)

        # Generate medical note
        medical_note = await build_medical_report(
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        return JSONResponse({"task_id": task_id, "medical_note": medical_note})
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_medical_note_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Domain insight routes
@methods_router.post("/domain/extract-domain-insights")
async def extract_domain_insights_endpoint(
    transcript: str = Body(..., media_type="text/plain"),
    task_id: Optional[str] = None,
    provider: Optional[str] = Query(
        None, description="LLM provider to use (azure, openai, lazarus)"
    ),
):
    """
    Extracts domain-specific insights from a transcript.

    This endpoint handles the sixth step of the medical transcription pipeline:
    extracting domain-specific insights like medical specialties, procedures,
    and other relevant information.

    - **transcript**: Transcript text to extract domain insights from
    - **task_id**: Optional task ID to associate with this extraction
    - **provider**: Optional LLM provider to use (azure, openai, lazarus)
    """
    try:
        # Create a unique task ID if not provided
        if not task_id:
            task_id = str(uuid.uuid4())

        # Create required objects for the pipeline
        response_usage = ResponseUsage()

        # Validate and convert provider
        llm_provider = None
        if provider:
            try:
                from acva_ai.llm.llm_orchestrator import LLMProvider

                llm_provider = LLMProvider(provider.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider '{provider}'. Valid options: azure, openai, lazarus",
                )

        # Create LLM orchestrator with specified provider
        from acva_ai.llm.llm_orchestrator import LLMOrchestrator

        orchestrator = validate_and_create_orchestrator(provider)

        # Extract domain insights
        domain_insights = await _process_domain_insights(
            task_id=task_id,
            transcript=transcript,
            response_usage=response_usage,
            llm_orchestrator=orchestrator,
        )

        # Ensure domain_insights is JSON serializable
        if hasattr(domain_insights, "model_dump"):
            # If it's a Pydantic model
            serialized_insights = domain_insights.model_dump()
        elif isinstance(domain_insights, list):
            # If it's a list of Pydantic models
            serialized_insights = [
                item.model_dump() if hasattr(item, "model_dump") else item
                for item in domain_insights
            ]
        else:
            # If it's already a dict or other JSON serializable type
            serialized_insights = domain_insights

        return JSONResponse(
            {"task_id": task_id, "domain_insights": serialized_insights}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in extract_domain_insights_endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))
