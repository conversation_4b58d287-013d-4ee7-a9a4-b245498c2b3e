OPENAI_API_KEY=
OPENAI_TRANSCRIPTION_API_KEY=
ELEVENLABS_API_KEY=

TMP_DATA_DIR=.data/tmp
CACHE_DIR=.data/cache
QDRANT_FOLDER = .data/qdrant_data
MINIO_FOLDER = .data/minio_data

API_KEY=API-KEY-MEDICAL-TRANSCRIPT
MONGODB =

FIRECRAWL_API =
BRAVE_API =

QDRANT_SERVER = localhost

API_WORKERS=4
API_WORKER_THREADS=2

ACVA_REPORT_FIELDS = "https://api.acva.leadliondev.ro/task/{task_id}/report-fields"
ACVA_CALLBACK_URL = "https://api.acva.leadliondev.ro/task/{task_id}/report-callback "

MONGO_DB_CONNECTION_STRING=

MINIO_SERVER = localhost
MINIO_PORT = 9000
MINIO_ROOT_USER=miniouser
MINIO_ROOT_PASSWORD=miniorootpassword